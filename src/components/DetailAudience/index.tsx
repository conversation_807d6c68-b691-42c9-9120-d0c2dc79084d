import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CircularProgress } from '@/components/ui/circularProgress';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from 'react-i18next';
import { Box } from '@/components/Box';
import { Progress } from '@/components/ui/progress';
import { RiCloseCircleFill, RiExternalLinkLine } from '@remixicon/react';
import React, { useState } from 'react';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { generateFBLink } from '@/utils/generateFBLink';
import { IAudienceDetail } from '@/types/audience';
import { generateTiktokLink } from '@/utils/generateTiktokLink';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { Button } from '@/components/ui/button';
import Modal from '@/components/Modal';
import { QUERY_KEY } from '@/utils/constants';
import { useQueryClient } from '@tanstack/react-query';

interface IDetailAudienceProps {
  detail: IAudienceDetail;
  setIdActive: (id: number) => void;
  act: string;
  type: 'FACEBOOK' | 'TIKTOK';
  historyContent?: React.ReactNode;
  updateContent?: React.ReactNode;
  onOpenHistoryModal?: (detail: IAudienceDetail) => void;
  onOpenUpdateModal?: (detail: IAudienceDetail) => void;
}

export const DetailAudience = ({ ...props }: IDetailAudienceProps) => {
  const {
    detail,
    act,
    type,
    setIdActive,
    historyContent,
    updateContent,
    onOpenHistoryModal,
    onOpenUpdateModal,
  } = props;
  const { t } = useTranslation();
  const { adsAccount } = useTiktokContext();
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [detailAudience, setDetailAudience] = useState<IAudienceDetail>(detail);
  const queryClient = useQueryClient();

  const audience = detailAudience ?? detail;
  const { status } = audience;
  const isEnabledLink = status === 'COMPLETED' || status === 'COMPLETED_WITH_ERRORS';

  const handleConvertStatus = (status: string, progress: number) => {
    if (type === 'TIKTOK') {
      switch (status) {
        case 'FAILED':
          return <Badge variant={'error'}>{t('common.failed')}</Badge>;
        case 'COMPLETED':
        case 'COMPLETED_WITH_ERRORS':
          return <Badge variant={'success'}>{t('common.success')}</Badge>;
        case 'PROCESSING':
          return (
            <Badge variant={'process'}>
              {t('common.inprogress')} {`${90}%`}
            </Badge>
          );
        case 'PENDING':
          return <Badge variant={'secondary'}>{t('common.pending')}</Badge>;
      }
    } else {
      switch (status) {
        case 'FAILED':
          return <Badge variant={'error'}>{t('common.failed')}</Badge>;
        case 'COMPLETED':
        case 'COMPLETED_WITH_ERRORS':
          return <Badge variant={'success'}>{t('common.success')}</Badge>;
        case 'PROCESSING':
          return (
            <Badge variant={'process'}>
              {t('common.inprogress')} {`${progress}%`}
            </Badge>
          );
        case 'PENDING':
          return <Badge variant={'secondary'}>{t('common.pending')}</Badge>;
      }
    }
  };

  const ContentComponent = () => {
    return (
      <>
        <Box className="gap-5">
          <div className="text-xs">{handleConvertStatus(audience.status, audience.progress)}</div>
          <p className="text-xs text-secondary">
            {Number(audience.processed_records).toLocaleString()}/
            {Number(audience.total_records).toLocaleString()} {t('common.contacts')}
          </p>
        </Box>
        <div className="bg-[#F0F0F0] rounded-full mt-2">
          <Progress value={audience.progress} className="h-[8px]" color={'#515667'} />
        </div>
      </>
    );
  };

  const TriggerComponent = () => {
    if (type === 'TIKTOK') {
      switch (status) {
        case 'FAILED':
          return <RiCloseCircleFill size={24} color={'#F53E3E'} />;
        case 'PROCESSING':
          return <CircularProgress value={90} size={'xl'} animateOnMount={true} />;
        case 'PENDING':
          return <CircularProgress value={0} size={'xl'} animateOnMount={true} />;
        default:
          return <></>;
      }
    } else {
      switch (status) {
        case 'FAILED':
          return <RiCloseCircleFill size={24} color={'#F53E3E'} />;
        case 'PROCESSING':
        case 'PENDING':
          return <CircularProgress value={audience.progress} size={'xl'} animateOnMount={true} />;
        default:
          return <></>;
      }
    }
  };

  const handleChangePopoverOpen = (open: boolean) => {
    setIsPopoverOpen(open);
    if (open) {
      if (
        detail.status !== 'COMPLETED' &&
        detail.status !== 'COMPLETED_WITH_ERRORS' &&
        detail.status !== 'FAILED'
      ) {
        handleFetchDetail();
      }
      if (detail.status === 'PENDING') {
        setIdActive(open ? audience.job_id : -1);
      }
    }
  };

  const handleFetchDetail = () => {
    switch (type) {
      case 'FACEBOOK':
        get({
          endpoint: ENDPOINTS.fb.log_detail(detail.job_id),
        }).then((res) => {
          const dataAudience = res?.data?.data as unknown as IAudienceDetail;
          setDetailAudience(dataAudience);
        });
        break;
      case 'TIKTOK':
        get({
          endpoint: ENDPOINTS.custom_audience.detail(detail.job_id.toString()),
        }).then((res) => {
          const dataAudience = res?.data?.data as unknown as IAudienceDetail;
          queryClient.invalidateQueries({
            queryKey: [QUERY_KEY.TIKTOK_AUDIENCE_DETAIL, detail.job_id],
          });
          setDetailAudience(dataAudience);
        });
        break;
      default:
        break;
    }
  };

  const gotoLink = () => {
    switch (type) {
      case 'FACEBOOK':
        return {
          href: generateFBLink(act),
          target: '_blank',
          title: t('common.facebookAds.goToFbAds'),
        };
      case 'TIKTOK':
        return {
          href: generateTiktokLink({
            cid: audience.audience_id,
            aadvid: adsAccount?.ad_account_id ?? '',
          }),
          target: '_blank',
          title: t('tiktokAds.goToTiktokAds'),
        };
      default:
        return { href: '/', target: '_self', title: '' };
    }
  };

  return (
    <div className="flex flex-col gap-0.5">
      <div className="flex w-full justify-between items-center gap-2">
        <div className="line-clamp-1 text-sm font-semibold truncate">{audience.audience_name}</div>
        <div className="relative">
          <Popover open={isPopoverOpen} onOpenChange={handleChangePopoverOpen}>
            <PopoverTrigger asChild>
              <div className="cursor-pointer">
                <TriggerComponent />
              </div>
            </PopoverTrigger>
            <PopoverContent className="p-2 min-w-[228px] w-full" align="center">
              <ContentComponent />
            </PopoverContent>
          </Popover>
        </div>
      </div>
      <div className="flex gap-2">
        {isEnabledLink && (
          <a
            className="flex items-center gap-1 text-[#2C9EFF] relative w-fit text-xs"
            href={gotoLink().href}
            target={gotoLink().target}
          >
            <div className="relative">
              {gotoLink().title}
              <div className="h-[1px] absolute bottom-0 w-full right-0 bg-[#2C9EFF]" />
            </div>
            <RiExternalLinkLine className="flex-shrink-0" size={16} />
          </a>
        )}
        {type === 'TIKTOK' && (
          <>
            {status !== 'PROCESSING' && status !== 'FAILED' && (
              <>
                {onOpenUpdateModal ? (
                  <Button
                    variant={'linkText'}
                    className="w-fit py-0 p-0 m-0 h-auto"
                    onClick={() => onOpenUpdateModal(audience)}
                  >
                    {t('common.update')}
                  </Button>
                ) : (
                  updateContent
                )}
              </>
            )}

            {/* Use callback if available, otherwise render modal */}
            {onOpenHistoryModal ? (
              <Button
                variant={'ghost'}
                className="w-fit py-0 p-0 m-0 h-auto"
                onClick={() => onOpenHistoryModal(audience)}
              >
                {t('common.history')}
              </Button>
            ) : (
              <Modal
                trigger={
                  <Button variant={'ghost'} className="w-fit py-0 p-0 m-0 h-auto">
                    {t('common.history')}
                  </Button>
                }
                className="max-w-[920px] w-full h-[592px]"
                title={
                  <div className="h-[40px]">
                    <p className="text-lg font-semibold text-big360Color-neutral-950">
                      {t('common.updateHistory')}
                    </p>
                    <p className="text-sm font-normal text-big360Color-neutral-700">
                      {t('common.customAudienceName')}
                    </p>
                  </div>
                }
              >
                {historyContent}
              </Modal>
            )}
          </>
        )}
      </div>
    </div>
  );
};
